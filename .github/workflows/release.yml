name: Release

on:
  push:
    tags:
      - "v*.*.*"

permissions:
  contents: read

jobs:

  release:
    permissions:
      contents: write  # for softprops/action-gh-release to create GitHub release
    name: Build release binaries
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest, macos-13 ]
    steps:
      - name: Checkout the repository
        uses: actions/checkout@master
      - name: Set the version
        run: echo "version=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_ENV
      - name: Set the platform
        run: echo "platform=$(tr '[A-Z]' '[a-z]' <<< $RUNNER_OS)" >> $GITHUB_ENV
      - name: Compile the project
        run: make clean && make
      - name: Build the artifact
        run: JANET_DIST_DIR=janet-${{ env.version }}-${{ env.platform }} make build/janet-${{ env.version }}-${{ env.platform }}-x64.tar.gz
      - name: Draft the release
        uses: softprops/action-gh-release@v1
        with:
          draft: true
          files: |
            build/*.gz
            build/janet.h
            build/c/janet.c
            build/c/shell.c

  release-arm:
    permissions:
      contents: write  # for softprops/action-gh-release to create GitHub release
    name: Build release binaries
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ macos-latest ]
    steps:
      - name: Checkout the repository
        uses: actions/checkout@master
      - name: Set the version
        run: echo "version=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_ENV
      - name: Set the platform
        run: echo "platform=$(tr '[A-Z]' '[a-z]' <<< $RUNNER_OS)" >> $GITHUB_ENV
      - name: Compile the project
        run: make clean && make
      - name: Build the artifact
        run: JANET_DIST_DIR=janet-${{ env.version }}-${{ env.platform }} make build/janet-${{ env.version }}-${{ env.platform }}-aarch64.tar.gz
      - name: Draft the release
        uses: softprops/action-gh-release@v1
        with:
          draft: true
          files: |
            build/*.gz
            build/janet.h
            build/c/janet.c
            build/c/shell.c

  release-windows:
    permissions:
      contents: write  # for softprops/action-gh-release to create GitHub release
    name: Build release binaries for windows
    runs-on: windows-latest
    steps:
      - name: Checkout the repository
        uses: actions/checkout@master
      - name: Setup MSVC
        uses: ilammy/msvc-dev-cmd@v1
      - name: Build the project
        shell: cmd
        run: build_win all
      - name: Draft the release
        uses: softprops/action-gh-release@v1
        with:
          draft: true
          files: |
            ./dist/*.zip
            ./*.zip
            ./*.msi

  release-cosmo:
    permissions:
      contents: write  # for softprops/action-gh-release to create GitHub release
    name: Build release binaries for Cosmo
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repository
        uses: actions/checkout@master
      - name: create build folder
        run: |
          sudo mkdir -p /sc
          sudo chmod -R 0777 /sc
      - name: setup Cosmopolitan Libc
        run: bash ./.github/cosmo/setup
      - name: Set the version
        run: echo "version=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_ENV
      - name: Set the platform
        run: echo "platform=cosmo" >> $GITHUB_ENV
      - name: build Janet APE binary
        run: bash ./.github/cosmo/build
      - name: push binary to github
        uses: softprops/action-gh-release@v1
        with:
          draft: true
          files: |
            /sc/cosmocc/janet.com
