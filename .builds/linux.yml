image: archlinux
sources:
- https://git.sr.ht/~bakpakin/janet
packages:
- meson
tasks:
- with-epoll: |
    cd janet
    meson setup with-epoll --buildtype=release
    cd with-epoll
    meson configure -Depoll=true
    ninja
    ninja test
- no-epoll: |
    cd janet
    meson setup no-epoll --buildtype=release
    cd no-epoll
    meson configure -Depoll=false
    ninja
    ninja test
    sudo ninja install
- meson_min: |
    cd janet
    meson setup build_meson_min --buildtype=release -Dsingle_threaded=true -Dnanbox=false -Ddynamic_modules=false -Ddocstrings=false -Dnet=false -Dsourcemaps=false -Dpeg=false -Dassembler=false -Dint_types=false -Dreduced_os=true -Dffi=false
    cd build_meson_min
    ninja
