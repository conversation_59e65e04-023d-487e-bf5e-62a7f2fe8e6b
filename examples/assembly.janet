# Example of dst bytecode assembly

# <PERSON><PERSON><PERSON><PERSON> sequence, implemented with naive recursion.
(def fibasm
  (asm
    '{:arity 1
      :bytecode @[(ltim 1 0 0x2)   # $1 = $0 < 2
                  (jmpif 1 :done)  # if ($1) goto :done
                  (lds 1)          # $1 = self
                  (addim 0 0 -0x1) # $0 = $0 - 1
                  (push 0)         # push($0), push argument for next function call
                  (call 2 1)       # $2 = call($1)
                  (addim 0 0 -0x1) # $0 = $0 - 1
                  (push 0)         # push($0)
                  (call 0 1)       # $0 = call($1)
                  (add 0 0 2)      # $0 = $0 + $2 (integers)
                  :done
                  (ret 0)          # return $0
]}))

# Test it

(defn testn
  [n]
  (print "fibasm(" n ") = " (fibasm n)))

(for i 0 10 (testn i))
